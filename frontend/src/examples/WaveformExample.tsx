import React from 'react';
import { useWavesurferWaveform } from '../hooks/useWavesurferWaveform';

/**
 * 简化的波形使用示例
 * 展示如何使用简化后的 useWavesurferWaveform hook
 */
const WaveformExample: React.FC = () => {
  // 示例音频源和时间范围
  const audioSrc = "https://example.com/audio.mp3";
  const timeFrame = { start: 0, end: 30000 }; // 0-30秒

  // 使用简化的 hook
  const {
    waveformDataUrl,
    isLoading,
    error,
    duration,
    clearCache
  } = useWavesurferWaveform(audioSrc, timeFrame, {
    width: 600,
    height: 80,
    waveColor: "#4A90E2",
    progressColor: "#2E5BBA",
    backgroundColor: "transparent",
    barWidth: 2,
    barGap: 1,
    normalize: true,
  });

  return (
    <div style={{ padding: '20px' }}>
      <h2>简化的音频波形示例</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <p>音频源: {audioSrc}</p>
        <p>时间范围: {timeFrame.start}ms - {timeFrame.end}ms</p>
        <p>音频时长: {duration.toFixed(2)}秒</p>
      </div>

      {isLoading && (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <p>正在生成波形...</p>
        </div>
      )}

      {error && (
        <div style={{ 
          padding: '20px', 
          backgroundColor: '#ffebee', 
          color: '#c62828',
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <p>错误: {error}</p>
        </div>
      )}

      {waveformDataUrl && !isLoading && (
        <div style={{ marginBottom: '20px' }}>
          <img 
            src={waveformDataUrl} 
            alt="Audio Waveform"
            style={{ 
              border: '1px solid #ddd',
              borderRadius: '4px',
              maxWidth: '100%'
            }}
          />
        </div>
      )}

      <div>
        <button 
          onClick={clearCache}
          style={{
            padding: '8px 16px',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          清理缓存
        </button>
      </div>

      <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        <h3>简化后的特性:</h3>
        <ul>
          <li>✅ 移除了复杂的自定义绘制逻辑</li>
          <li>✅ 直接使用 wavesurfer.js 的内置功能</li>
          <li>✅ 简化的缓存管理</li>
          <li>✅ 更少的代码，更好的维护性</li>
          <li>✅ 保留了核心功能：波形生成、缓存、错误处理</li>
          <li>✅ 30秒超时保护</li>
          <li>✅ 支持时间范围切片</li>
        </ul>
      </div>
    </div>
  );
};

export default WaveformExample;

import { useRef, useEffect, useState, useCallback, useMemo } from "react";
import WaveSurfer from "wavesurfer.js";
import { debounce } from "lodash";

interface WaveformOptions {
  width: number;
  height: number;
  waveColor: string;
  progressColor: string;
  backgroundColor: string;
  barWidth?: number;
  barGap?: number;
  normalize?: boolean;
}

// 简化的缓存管理
const waveformCache = new Map<string, string>();
const MAX_CACHE_SIZE = 50;

/**
 * 简化的缓存管理
 */
const manageCacheSize = () => {
  if (waveformCache.size > MAX_CACHE_SIZE) {
    const keysToDelete = Array.from(waveformCache.keys()).slice(0, 10);
    keysToDelete.forEach((key) => waveformCache.delete(key));
  }
};

const getCachedWaveform = (key: string): string | undefined => {
  return waveformCache.get(key);
};

const setCachedWaveform = (key: string, value: string) => {
  manageCacheSize();
  waveformCache.set(key, value);
};

const clearWaveformCache = () => {
  waveformCache.clear();
};

const clearWaveformCacheForUrl = (url: string) => {
  const keysToDelete: string[] = [];
  for (const key of waveformCache.keys()) {
    if (key.includes(url)) {
      keysToDelete.push(key);
    }
  }
  keysToDelete.forEach((key) => waveformCache.delete(key));
};

/**
 * 错误处理工具
 */
const handleWavesurferError = (err: any, audioSrc: string): string => {
  console.error("Wavesurfer error:", err);

  if (
    err &&
    (err.name === "EncodingError" || err.message?.includes("decode"))
  ) {
    console.warn(
      "Audio decoding failed, possibly due to CORS or invalid audio data:",
      audioSrc
    );
    return "Audio format not supported or CORS issue";
  }

  return "Failed to load audio waveform";
};

/**
 * 简化的 Wavesurfer.js 音频波形 Hook
 * @param audioSrc 音频源URL
 * @param timeFrame 时间帧信息
 * @param options 波形配置选项
 * @returns 波形相关的状态和方法
 */
export const useWavesurferWaveform = (
  audioSrc: string,
  timeFrame: { start: number; end: number },
  options: Partial<WaveformOptions> = {}
) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const wavesurferRef = useRef<WaveSurfer | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const [waveformDataUrl, setWaveformDataUrl] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [duration, setDuration] = useState(0);

  // 简化的默认配置
  const defaultOptions: WaveformOptions = useMemo(
    () => ({
      width: 800,
      height: 60,
      waveColor: "#4A90E2",
      progressColor: "#2E5BBA",
      backgroundColor: "transparent",
      barWidth: 2,
      barGap: 1,
      normalize: true,
      ...options,
    }),
    [
      options.width,
      options.height,
      options.waveColor,
      options.progressColor,
      options.backgroundColor,
      options.barWidth,
      options.barGap,
      options.normalize,
    ]
  );

  // 简化的波形生成函数
  const generateWaveformImage = useCallback(
    async (wavesurfer: WaveSurfer): Promise<string> => {
      return new Promise((resolve, reject) => {
        try {
          // 创建临时画布
          const canvas = document.createElement("canvas");
          canvas.width = defaultOptions.width;
          canvas.height = defaultOptions.height;
          const ctx = canvas.getContext("2d");

          if (!ctx) {
            reject(new Error("Failed to get canvas context"));
            return;
          }

          // 设置背景
          if (defaultOptions.backgroundColor !== "transparent") {
            ctx.fillStyle = defaultOptions.backgroundColor;
            ctx.fillRect(0, 0, defaultOptions.width, defaultOptions.height);
          }

          // 获取波形数据并绘制
          const decodedData = wavesurfer.getDecodedData();
          if (!decodedData) {
            reject(new Error("No decoded data available"));
            return;
          }

          const peaks = decodedData.getChannelData(0);
          if (!peaks) {
            reject(new Error("No peaks data available"));
            return;
          }

          // 计算时间范围内的数据
          const totalDuration = wavesurfer.getDuration();
          const startRatio = timeFrame.start / 1000 / totalDuration;
          const endRatio = timeFrame.end / 1000 / totalDuration;

          const startIndex = Math.floor(startRatio * peaks.length);
          const endIndex = Math.floor(endRatio * peaks.length);
          const segmentPeaks = peaks.slice(startIndex, endIndex);

          // 简化的波形绘制
          const barWidth = defaultOptions.barWidth || 2;
          const barGap = defaultOptions.barGap || 1;
          const totalBarWidth = barWidth + barGap;
          const numBars = Math.floor(defaultOptions.width / totalBarWidth);
          const samplesPerBar = Math.max(
            1,
            Math.floor(segmentPeaks.length / numBars)
          );

          ctx.fillStyle = defaultOptions.waveColor;

          for (let i = 0; i < numBars; i++) {
            const start = i * samplesPerBar;
            const end = start + samplesPerBar;
            let max = 0;

            for (let j = start; j < end && j < segmentPeaks.length; j++) {
              max = Math.max(max, Math.abs(segmentPeaks[j]));
            }

            if (defaultOptions.normalize) {
              max = Math.min(max * 2, 1);
            }

            const barHeight = max * defaultOptions.height * 0.8;
            const x = i * totalBarWidth;
            const y = (defaultOptions.height - barHeight) / 2;

            ctx.fillRect(x, y, barWidth, barHeight);
          }

          resolve(canvas.toDataURL());
        } catch (err) {
          reject(err);
        }
      });
    },
    [defaultOptions, timeFrame]
  );

  // 检查音频源可访问性
  const checkAudioAccessibility = useCallback(async (src: string) => {
    if (src.includes("/api/proxy/jamendo-audio")) {
      try {
        const response = await fetch(src, { method: "HEAD" });
        if (!response.ok) {
          throw new Error(`Audio source not accessible: ${response.status}`);
        }
      } catch (fetchError) {
        console.warn("Audio source check failed:", fetchError);
        throw new Error("Audio source not accessible");
      }
    }
  }, []);

  // 防抖的波形生成函数
  const generateWaveformDebounced = useCallback(
    debounce(async (src: string, key: string) => {
      if (!src) return;

      // 取消之前的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      // 首先检查缓存
      const cachedWaveform = getCachedWaveform(key);
      if (cachedWaveform) {
        setWaveformDataUrl(cachedWaveform);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // 检查音频源可访问性
        await checkAudioAccessibility(src);

        // 清理之前的实例
        if (wavesurferRef.current) {
          wavesurferRef.current.destroy();
        }

        // 创建新的 Wavesurfer 实例
        const wavesurfer = WaveSurfer.create({
          container: document.createElement("div"), // 临时容器
          waveColor: defaultOptions.waveColor,
          progressColor: defaultOptions.progressColor,
          height: defaultOptions.height,
          normalize: defaultOptions.normalize,
          interact: false,
          barWidth: defaultOptions.barWidth,
          barGap: defaultOptions.barGap,
        });

        wavesurferRef.current = wavesurfer;

        // 监听加载完成事件
        const handleReady = async () => {
          if (abortControllerRef.current?.signal.aborted) {
            return;
          }

          try {
            setDuration(wavesurfer.getDuration());

            // 生成波形图片
            const dataUrl = await generateWaveformImage(wavesurfer);

            if (abortControllerRef.current?.signal.aborted) {
              return;
            }

            // 缓存结果
            setCachedWaveform(key, dataUrl);

            setWaveformDataUrl(dataUrl);
            setIsLoading(false);
          } catch (err) {
            if (!abortControllerRef.current?.signal.aborted) {
              setError("Failed to generate waveform");
              setIsLoading(false);
            }
          }
        };

        const handleError = (err: any) => {
          if (!abortControllerRef.current?.signal.aborted) {
            setError(handleWavesurferError(err, src));
            setIsLoading(false);

            // 清理失败的Wavesurfer实例
            if (wavesurfer) {
              try {
                wavesurfer.destroy();
              } catch (destroyErr) {
                console.warn(
                  "Error destroying failed wavesurfer instance:",
                  destroyErr
                );
              }
            }
          }
        };

        wavesurfer.on("ready", handleReady);
        wavesurfer.on("error", handleError);

        // 加载音频，添加超时处理
        const loadPromise = wavesurfer.load(src);

        // 设置超时，防止长时间等待
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error("Audio load timeout"));
          }, 30000); // 30秒超时
        });

        await Promise.race([loadPromise, timeoutPromise]);
      } catch (err) {
        if (!abortControllerRef.current?.signal.aborted) {
          setError("Failed to create waveform");
          setIsLoading(false);
        }
      }
    }, 300),
    [defaultOptions, generateWaveformImage, checkAudioAccessibility]
  );

  // 生成稳定的缓存键
  const cacheKey = useMemo(() => {
    const optionsHash = JSON.stringify({
      width: defaultOptions.width,
      height: defaultOptions.height,
      waveColor: defaultOptions.waveColor,
      barWidth: defaultOptions.barWidth,
      barGap: defaultOptions.barGap,
      normalize: defaultOptions.normalize,
    });
    return `${audioSrc}_${timeFrame.start}_${timeFrame.end}_${btoa(
      optionsHash
    )}`;
  }, [audioSrc, timeFrame.start, timeFrame.end, defaultOptions]);

  // 初始缓存检查
  useEffect(() => {
    if (audioSrc && cacheKey) {
      const cachedWaveform = getCachedWaveform(cacheKey);
      if (cachedWaveform) {
        setWaveformDataUrl(cachedWaveform);
        setIsLoading(false);
        return;
      }
    }
  }, [audioSrc, cacheKey]);

  // 当音频源或时间帧改变时重新生成波形
  useEffect(() => {
    if (audioSrc && !getCachedWaveform(cacheKey)) {
      generateWaveformDebounced(audioSrc, cacheKey);
    }

    return () => {
      generateWaveformDebounced.cancel();
    };
  }, [audioSrc, cacheKey, generateWaveformDebounced]);

  // 监听全局清理事件
  useEffect(() => {
    const handleClearWaveformCache = (event: CustomEvent) => {
      const { audioSrc: targetAudioSrc } = event.detail;
      if (targetAudioSrc && targetAudioSrc === audioSrc) {
        clearWaveformCacheForUrl(targetAudioSrc);
      }
    };

    window.addEventListener(
      "clearWaveformCache",
      handleClearWaveformCache as EventListener
    );

    return () => {
      window.removeEventListener(
        "clearWaveformCache",
        handleClearWaveformCache as EventListener
      );
    };
  }, [audioSrc]);

  // 清理函数
  useEffect(() => {
    return () => {
      // 取消所有进行中的操作
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // 取消防抖函数
      generateWaveformDebounced.cancel();

      // 清理Wavesurfer实例
      if (wavesurferRef.current) {
        try {
          wavesurferRef.current.destroy();
        } catch (err) {
          console.warn("Error destroying wavesurfer on cleanup:", err);
        }
        wavesurferRef.current = null;
      }

      // 清理状态
      setWaveformDataUrl("");
      setIsLoading(false);
      setError(null);
    };
  }, [generateWaveformDebounced]);

  return {
    containerRef,
    waveformDataUrl,
    isLoading,
    error,
    duration,
    wavesurfer: wavesurferRef.current,
    clearCache: clearWaveformCache,
  };
};

// 导出一个用于清理全局缓存的函数
export const clearGlobalWaveformCache = () => {
  clearWaveformCache();
};

// 导出一个用于清理特定URL相关缓存的函数
export { clearWaveformCacheForUrl };
